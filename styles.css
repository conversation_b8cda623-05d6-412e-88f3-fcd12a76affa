/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #1a1a2e;
    --secondary-color: #16213e;
    --accent-color: #e94560;
    --gold-color: #ffd700;
    --text-light: #ffffff;
    --text-gray: #b8b8b8;
    --text-dark: #333333;
    --gradient-primary: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    --gradient-accent: linear-gradient(135deg, #e94560 0%, #ffd700 100%);
    --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.2);
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-light);
    background: var(--primary-color);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.8rem; }
h4 { font-size: 1.4rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-gray);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-accent);
    color: var(--text-light);
    box-shadow: var(--shadow-medium);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.btn-secondary {
    background: transparent;
    color: var(--text-light);
    border: 2px solid var(--gold-color);
}

.btn-secondary:hover {
    background: var(--gold-color);
    color: var(--primary-color);
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h2 {
    color: var(--gold-color);
    margin: 0;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--gold-color);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-light);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--gradient-primary);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="%23ffd700" opacity="0.3"><animate attributeName="opacity" values="0.3;1;0.3" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="40" r="1" fill="%23e94560" opacity="0.4"><animate attributeName="opacity" values="0.4;1;0.4" dur="3s" repeatCount="indefinite"/></circle><circle cx="40" cy="80" r="1" fill="%23ffd700" opacity="0.2"><animate attributeName="opacity" values="0.2;1;0.2" dur="4s" repeatCount="indefinite"/></circle></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.hero .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-title {
    font-size: 4rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
}

.highlight {
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.3rem;
    margin-bottom: 2rem;
    color: var(--text-gray);
}

.hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.ai-human-collaboration {
    display: flex;
    align-items: center;
    gap: 2rem;
    padding: 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.human-icon, .ai-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-light);
}

.human-icon {
    background: var(--gradient-accent);
}

.ai-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.connection-line {
    width: 60px;
    height: 3px;
    background: var(--gradient-accent);
    position: relative;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--text-gray);
}

/* Features Section */
.features {
    background: var(--secondary-color);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--gold-color);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-light);
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* Testimonials */
.testimonials {
    background: var(--primary-color);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.testimonial-content p {
    font-style: italic;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    font-size: 2.5rem;
    color: var(--gold-color);
}

.author-info h4 {
    margin: 0;
    color: var(--text-light);
}

.author-info span {
    color: var(--text-gray);
    font-size: 0.9rem;
}

.cta-text {
    text-align: center;
}

.cta-text p {
    font-size: 1.3rem;
    color: var(--gold-color);
}

/* How It Works */
.how-it-works {
    background: var(--secondary-color);
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1.5rem;
    color: var(--text-light);
}

.step h3 {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* Signup Section */
.signup {
    background: var(--primary-color);
    padding: 6rem 0;
}

.signup-card {
    max-width: 600px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(255, 215, 0, 0.2);
    backdrop-filter: blur(10px);
}

.signup-form {
    margin-top: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-light);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--gold-color);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-gray);
}

.privacy-note {
    text-align: center;
    font-size: 0.9rem;
    color: var(--text-gray);
    margin-top: 1rem;
}

/* Roadmap Section */
.roadmap {
    background: var(--secondary-color);
}

.roadmap-timeline {
    max-width: 800px;
    margin: 0 auto 4rem;
    position: relative;
}

.roadmap-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-accent);
}

.timeline-item {
    position: relative;
    padding-left: 80px;
    margin-bottom: 3rem;
}

.timeline-marker {
    position: absolute;
    left: 20px;
    top: 0;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border: 3px solid var(--gold-color);
    border-radius: 50%;
}

.timeline-item.active .timeline-marker {
    background: var(--gold-color);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.timeline-content {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.timeline-item.active .timeline-content {
    border-color: var(--gold-color);
    background: rgba(255, 215, 0, 0.1);
}

.timeline-content h3 {
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.phase-status {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    background: var(--gradient-accent);
    color: var(--text-light);
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

.community-invite {
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.community-invite p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.community-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* FAQ Section */
.faq {
    background: var(--primary-color);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.1);
    border-radius: 10px;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: var(--gold-color);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.02);
}

.faq-question h3 {
    margin: 0;
    color: var(--text-light);
    font-size: 1.2rem;
}

.faq-question i {
    color: var(--gold-color);
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-gray);
    margin: 0;
}

/* Footer */
.footer {
    background: var(--primary-color);
    border-top: 1px solid rgba(255, 215, 0, 0.2);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--gold-color);
    margin-bottom: 1rem;
}

.footer-section p,
.footer-section a {
    color: var(--text-gray);
    text-decoration: none;
    margin-bottom: 0.5rem;
    display: block;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--gold-color);
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.social-links a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: var(--text-gray);
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-cta {
        justify-content: center;
    }

    .ai-human-collaboration {
        gap: 1rem;
        padding: 1.5rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .steps-container {
        grid-template-columns: 1fr;
    }

    .roadmap-timeline::before {
        left: 15px;
    }

    .timeline-item {
        padding-left: 50px;
    }

    .timeline-marker {
        left: 5px;
    }

    .community-buttons {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .social-links {
        align-items: center;
    }
}
