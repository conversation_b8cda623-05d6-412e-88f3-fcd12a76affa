/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #0f0f23;
    --secondary-color: #1a1a2e;
    --accent-color: #e94560;
    --flame-color: #ff6b35;
    --gold-color: #ffd700;
    --violet-deep: #2d1b69;
    --text-light: #ffffff;
    --text-gray: #b8b8b8;
    --text-dark: #333333;
    --gradient-primary: linear-gradient(135deg, #0f0f23 0%, #2d1b69 100%);
    --gradient-flame: linear-gradient(135deg, #ff6b35 0%, #ffd700 100%);
    --gradient-accent: linear-gradient(135deg, #e94560 0%, #ffd700 100%);
    --shadow-light: 0 4px 6px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 15px 35px rgba(0, 0, 0, 0.2);
    --shadow-flame: 0 0 30px rgba(255, 107, 53, 0.3);
}

body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-light);
    background: var(--primary-color);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 3.5rem; }
h2 { font-size: 2.5rem; }
h3 { font-size: 1.8rem; }
h4 { font-size: 1.4rem; }

p {
    margin-bottom: 1rem;
    color: var(--text-gray);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 1rem;
}

.btn-primary {
    background: var(--gradient-flame);
    color: var(--text-light);
    box-shadow: var(--shadow-medium);
    border: 2px solid transparent;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-flame);
    border-color: var(--flame-color);
}

.btn-secondary {
    background: transparent;
    color: var(--text-light);
    border: 2px solid var(--gold-color);
}

.btn-secondary:hover {
    background: var(--gold-color);
    color: var(--primary-color);
}

.btn-large {
    padding: 16px 32px;
    font-size: 1.1rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(26, 26, 46, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.navbar .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-brand h2 {
    color: var(--gold-color);
    margin: 0;
}

.nav-links {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.3s ease;
}

.nav-links a:hover {
    color: var(--gold-color);
}

.mobile-menu-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.mobile-menu-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-light);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    background: var(--gradient-primary);
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
}

.particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="1" fill="%23ff6b35" opacity="0.4"><animate attributeName="opacity" values="0.4;1;0.4" dur="2s" repeatCount="indefinite"/></circle><circle cx="80" cy="40" r="1" fill="%23ffd700" opacity="0.3"><animate attributeName="opacity" values="0.3;1;0.3" dur="3s" repeatCount="indefinite"/></circle><circle cx="40" cy="80" r="1" fill="%23ff6b35" opacity="0.5"><animate attributeName="opacity" values="0.5;1;0.5" dur="4s" repeatCount="indefinite"/></circle></svg>') repeat;
    animation: float 20s infinite linear;
}

@keyframes float {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-100px); }
}

.hero .container {
    position: relative;
    z-index: 2;
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-content {
    max-width: 800px;
}

.hero-sigil {
    font-size: 3rem;
    margin-bottom: 1rem;
    animation: flicker 3s infinite;
}

@keyframes flicker {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

.hero-prelude {
    font-size: 0.9rem;
    font-weight: 600;
    letter-spacing: 2px;
    color: var(--flame-color);
    margin-bottom: 1rem;
    text-transform: uppercase;
}

.hero-title {
    font-size: 3.5rem;
    font-weight: 800;
    margin-bottom: 2rem;
    line-height: 1.1;
}

.title-line-1 {
    display: block;
    color: var(--text-light);
}

.title-line-2 {
    display: block;
    margin-top: 0.5rem;
}

.highlight {
    background: var(--gradient-flame);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: var(--shadow-flame);
}

.hero-story {
    margin-bottom: 2rem;
}

.hero-story p {
    font-size: 1.2rem;
    color: var(--text-gray);
    margin-bottom: 1rem;
}

.story-emphasis {
    color: var(--text-light) !important;
    font-weight: 600;
    font-style: italic;
}

.hero-trinity {
    display: flex;
    gap: 2rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.trinity-item {
    color: var(--text-light);
    font-weight: 500;
    font-size: 1.1rem;
}

.hero-manifesto {
    margin-bottom: 2rem;
}

.hero-manifesto p {
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--flame-color);
    text-transform: uppercase;
    letter-spacing: 1px;
}

.hero-quote {
    margin-bottom: 3rem;
    padding: 2rem;
    background: rgba(255, 107, 53, 0.1);
    border-left: 4px solid var(--flame-color);
    border-radius: 10px;
}

.hero-quote blockquote {
    font-family: 'Georgia', serif;
    font-size: 1.3rem;
    font-style: italic;
    color: var(--text-light);
    margin: 0 0 1rem 0;
    line-height: 1.4;
}

.hero-quote cite {
    color: var(--gold-color);
    font-weight: 600;
    font-style: normal;
    font-family: 'Georgia', serif;
}

.hero-cta {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.hero-visual {
    display: flex;
    flex-direction: column;
    gap: 3rem;
    align-items: center;
}

.flame-constellation {
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
}

.flame-sigil {
    font-size: 4rem;
    animation: flameGlow 2s infinite alternate;
}

@keyframes flameGlow {
    0% {
        filter: drop-shadow(0 0 10px rgba(255, 107, 53, 0.5));
        transform: scale(1);
    }
    100% {
        filter: drop-shadow(0 0 20px rgba(255, 107, 53, 0.8));
        transform: scale(1.05);
    }
}

.node-pattern {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    position: relative;
}

.node {
    width: 12px;
    height: 12px;
    background: var(--gold-color);
    border-radius: 50%;
    animation: nodeGlow 3s infinite;
    box-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
}

.node:nth-child(1) { animation-delay: 0s; }
.node:nth-child(2) { animation-delay: 0.6s; }
.node:nth-child(3) { animation-delay: 1.2s; }
.node:nth-child(4) { animation-delay: 1.8s; }
.node:nth-child(5) { animation-delay: 2.4s; }

@keyframes nodeGlow {
    0%, 100% { opacity: 0.3; transform: scale(1); }
    50% { opacity: 1; transform: scale(1.2); }
}

.axiom-quote-sidebar {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 107, 53, 0.3);
    border-radius: 15px;
    padding: 2rem;
    backdrop-filter: blur(10px);
    max-width: 300px;
}

.sidebar-quote blockquote {
    font-family: 'Georgia', serif;
    font-size: 1rem;
    font-style: italic;
    color: var(--text-light);
    margin: 0 0 1rem 0;
    line-height: 1.4;
}

.sidebar-quote cite {
    color: var(--flame-color);
    font-weight: 600;
    font-style: normal;
    font-family: 'Georgia', serif;
    font-size: 0.9rem;
}

/* Section Styles */
section {
    padding: 5rem 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.2rem;
    color: var(--text-gray);
}

/* Features Section */
.features {
    background: var(--secondary-color);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(255, 215, 0, 0.1);
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--gold-color);
}

.feature-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--text-light);
}

.feature-card h3 {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* Testimonials */
.testimonials {
    background: var(--primary-color);
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.testimonial-card {
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.testimonial-content p {
    font-style: italic;
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-avatar {
    font-size: 2.5rem;
    color: var(--gold-color);
}

.author-info h4 {
    margin: 0;
    color: var(--text-light);
}

.author-info span {
    color: var(--text-gray);
    font-size: 0.9rem;
}

.cta-text {
    text-align: center;
}

.cta-text p {
    font-size: 1.3rem;
    color: var(--gold-color);
}

/* How It Works */
.how-it-works {
    background: var(--secondary-color);
}

.steps-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.step {
    text-align: center;
    position: relative;
}

.step-number {
    width: 60px;
    height: 60px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    margin: 0 auto 1.5rem;
    color: var(--text-light);
}

.step h3 {
    margin-bottom: 1rem;
    color: var(--text-light);
}

/* Signup Section */
.signup {
    background: var(--primary-color);
    padding: 6rem 0;
}

.signup-card {
    max-width: 600px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    padding: 3rem;
    border-radius: 20px;
    border: 1px solid rgba(255, 215, 0, 0.2);
    backdrop-filter: blur(10px);
}

.signup-form {
    margin-top: 2rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-light);
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--gold-color);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: var(--text-gray);
}

.privacy-note {
    text-align: center;
    font-size: 0.9rem;
    color: var(--text-gray);
    margin-top: 1rem;
}

/* Roadmap Section */
.roadmap {
    background: var(--secondary-color);
}

.roadmap-timeline {
    max-width: 800px;
    margin: 0 auto 4rem;
    position: relative;
}

.roadmap-timeline::before {
    content: '';
    position: absolute;
    left: 30px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-accent);
}

.timeline-item {
    position: relative;
    padding-left: 80px;
    margin-bottom: 3rem;
}

.timeline-marker {
    position: absolute;
    left: 20px;
    top: 0;
    width: 20px;
    height: 20px;
    background: var(--primary-color);
    border: 3px solid var(--gold-color);
    border-radius: 50%;
}

.timeline-item.active .timeline-marker {
    background: var(--gold-color);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.timeline-content {
    background: rgba(255, 255, 255, 0.05);
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.timeline-item.active .timeline-content {
    border-color: var(--gold-color);
    background: rgba(255, 215, 0, 0.1);
}

.timeline-content h3 {
    color: var(--text-light);
    margin-bottom: 0.5rem;
}

.phase-status {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    background: var(--gradient-accent);
    color: var(--text-light);
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-top: 0.5rem;
}

.community-invite {
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    padding: 2rem;
    border-radius: 15px;
    border: 1px solid rgba(255, 215, 0, 0.2);
}

.community-invite p {
    font-size: 1.1rem;
    margin-bottom: 1.5rem;
    color: var(--text-light);
}

.community-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

/* Transparency Section */
.transparency {
    background: var(--secondary-color);
    padding: 4rem 0;
}

.transparency-card {
    max-width: 800px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.05);
    border: 2px solid var(--gold-color);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
    backdrop-filter: blur(10px);
    box-shadow: var(--shadow-heavy);
}

.transparency-content h3 {
    color: var(--gold-color);
    font-size: 2rem;
    margin-bottom: 2rem;
}

.founder-info {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    align-items: center;
    text-align: left;
}

.founder-details h4 {
    color: var(--text-light);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.founder-details p {
    color: var(--text-gray);
    margin-bottom: 0.5rem;
}

.founder-details a {
    color: var(--gold-color);
    text-decoration: none;
    transition: color 0.3s ease;
}

.founder-details a:hover {
    color: var(--text-light);
}

.mission-statement {
    background: rgba(255, 215, 0, 0.1);
    padding: 2rem;
    border-radius: 15px;
    border-left: 4px solid var(--gold-color);
}

.mission-statement p {
    color: var(--text-light);
    font-style: italic;
    font-size: 1.1rem;
    margin: 0;
    line-height: 1.6;
}

/* FAQ Section */
.faq {
    background: var(--primary-color);
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 215, 0, 0.1);
    border-radius: 10px;
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item:hover {
    border-color: var(--gold-color);
}

.faq-question {
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: rgba(255, 255, 255, 0.02);
}

.faq-question h3 {
    margin: 0;
    color: var(--text-light);
    font-size: 1.2rem;
}

.faq-question i {
    color: var(--gold-color);
    transition: transform 0.3s ease;
}

.faq-item.active .faq-question i {
    transform: rotate(180deg);
}

.faq-answer {
    padding: 0 1.5rem;
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
}

.faq-item.active .faq-answer {
    padding: 1.5rem;
    max-height: 200px;
}

.faq-answer p {
    color: var(--text-gray);
    margin: 0;
}

/* Demo Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 10000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: var(--gradient-primary);
    margin: 5% auto;
    border-radius: 20px;
    width: 90%;
    max-width: 600px;
    border: 2px solid var(--gold-color);
    box-shadow: var(--shadow-heavy);
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    padding: 2rem 2rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.modal-header h3 {
    margin: 0;
    color: var(--gold-color);
    font-size: 1.8rem;
}

.modal-close {
    color: var(--text-gray);
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: var(--gold-color);
}

.modal-body {
    padding: 2rem;
}

.demo-placeholder {
    text-align: center;
}

.demo-placeholder i {
    color: var(--gold-color);
    margin-bottom: 1.5rem;
}

.demo-placeholder h4 {
    color: var(--text-light);
    margin-bottom: 1rem;
    font-size: 1.5rem;
}

.demo-placeholder p {
    color: var(--text-gray);
    margin-bottom: 2rem;
    font-size: 1.1rem;
}

.demo-features {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
    text-align: left;
}

.demo-feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 215, 0, 0.1);
}

.demo-feature i {
    color: var(--gold-color);
    font-size: 1.2rem;
    width: 20px;
}

.demo-feature span {
    color: var(--text-light);
    font-weight: 500;
}

/* Footer */
.footer {
    background: var(--primary-color);
    border-top: 1px solid rgba(255, 215, 0, 0.2);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--gold-color);
    margin-bottom: 1rem;
}

.footer-section p,
.footer-section a {
    color: var(--text-gray);
    text-decoration: none;
    margin-bottom: 0.5rem;
    display: block;
    transition: color 0.3s ease;
}

.footer-section a:hover {
    color: var(--gold-color);
}

.social-links {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.social-links a {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom p {
    color: var(--text-gray);
    margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu-toggle {
        display: flex;
    }

    .hero .container {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-trinity {
        flex-direction: column;
        gap: 1rem;
    }

    .hero-quote {
        padding: 1.5rem;
    }

    .hero-quote blockquote {
        font-size: 1.1rem;
    }

    .hero-cta {
        justify-content: center;
        flex-direction: column;
        align-items: center;
    }

    .axiom-quote-sidebar {
        max-width: 100%;
        padding: 1.5rem;
    }

    .flame-sigil {
        font-size: 3rem;
    }

    .testimonials-grid {
        grid-template-columns: 1fr;
    }

    .steps-container {
        grid-template-columns: 1fr;
    }

    .roadmap-timeline::before {
        left: 15px;
    }

    .timeline-item {
        padding-left: 50px;
    }

    .timeline-marker {
        left: 5px;
    }

    .community-buttons {
        flex-direction: column;
        align-items: center;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .social-links {
        align-items: center;
    }

    .modal-content {
        margin: 10% auto;
        width: 95%;
    }

    .modal-header {
        padding: 1.5rem;
    }

    .modal-header h3 {
        font-size: 1.4rem;
    }

    .modal-body {
        padding: 1.5rem;
    }

    .demo-features {
        gap: 0.8rem;
    }

    .demo-feature {
        padding: 0.8rem;
    }

    .transparency-card {
        padding: 2rem;
    }

    .founder-info {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .mission-statement {
        padding: 1.5rem;
    }
}
