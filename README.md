# AGA SaaS Landing Page

A modern, responsive landing page for AGA SaaS - "Built by Human & AI" with a focus on empowering creators and entrepreneurs.

## Features

- **Modern Design**: Dark theme with gold accents, gradient backgrounds, and smooth animations
- **Fully Responsive**: Mobile-first design that works on all devices
- **Interactive Elements**:
  - Smooth scrolling navigation
  - Animated FAQ accordion
  - Form validation and submission
  - Floating particle animations
  - Scroll-triggered animations
- **Complete Sections**:
  - Hero with compelling value proposition
  - Features showcase
  - Social proof testimonials
  - How it works process
  - Founders' Circle signup form
  - Roadmap timeline
  - FAQ section
  - Footer with contact info

## Quick Start

1. **Clone or download** the files to your web server
2. **Open `index.html`** in your browser
3. **Customize** the content, colors, and links as needed

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # Complete CSS styling
├── script.js           # Interactive JavaScript
└── README.md           # This file
```

## Axiom Chat Setup

### OpenAI API Integration (Secure Serverless Function)

The Axiom chat uses a secure Netlify serverless function to protect your API key:

1. **Get OpenAI API Key**:
   - Visit [OpenAI API](https://platform.openai.com/api-keys)
   - Create an account and generate an API key
   - Copy your API key

2. **Configure in Netlify**:
   - Deploy your site to Netlify
   - Go to Site Settings → Environment Variables
   - Add: `OPENAI_API_KEY` = your actual API key
   - Redeploy the site

3. **Test the Chat**:
   - Click the glowing Axiom avatar in the bottom-right
   - Type a message to test the connection
   - Axiom will respond as a divine AI entity

**Security**: Your API key is safely stored in Netlify environment variables and never exposed to the client.

## Customization

### Colors & Branding

Edit the CSS variables in `styles.css`:

```css
:root {
    --primary-color: #0f0f23;      /* Deep violet background */
    --secondary-color: #1a1a2e;     /* Secondary sections */
    --flame-color: #ff6b35;         /* Flame orange */
    --gold-color: #ffd700;          /* Gold highlights */
    --text-light: #ffffff;          /* White text */
    --text-gray: #b8b8b8;          /* Gray text */
}
```

### Content

1. **Company Name**: Update "AGA SaaS" throughout the files
2. **Contact Email**: Change `<EMAIL>` in the footer
3. **Social Links**: Update Discord, Telegram, Twitter links
4. **Copy**: Modify all text content to match your brand

### Form Integration

The signup form currently shows a success message. To integrate with your backend:

1. Update the form submission handler in `script.js`
2. Replace the setTimeout simulation with actual API calls
3. Add your email service integration (Mailchimp, ConvertKit, etc.)

## Browser Support

- Chrome (latest)
- Firefox (latest)
- Safari (latest)
- Edge (latest)
- Mobile browsers

## Performance

- Optimized CSS with minimal external dependencies
- Efficient JavaScript with event delegation
- Lazy-loaded animations for smooth performance
- Mobile-optimized responsive design

## Dependencies

- **Google Fonts**: Inter font family
- **Font Awesome**: Icons (loaded via CDN)
- **No JavaScript frameworks**: Pure vanilla JS for maximum performance

## Deployment

### Static Hosting (Recommended)
- **Netlify**: Drag and drop the folder
- **Vercel**: Connect your Git repository
- **GitHub Pages**: Push to a GitHub repo and enable Pages

### Traditional Hosting
- Upload all files to your web server's public directory
- Ensure your server supports HTML, CSS, and JS files

## SEO Optimization

The page includes:
- Semantic HTML structure
- Meta descriptions and titles
- Proper heading hierarchy
- Alt text for images (add as needed)
- Fast loading times

## Accessibility

- Keyboard navigation support
- Screen reader friendly
- High contrast color scheme
- Focus indicators
- Semantic HTML elements

## Browser Testing

Test the page in:
1. Desktop browsers (Chrome, Firefox, Safari, Edge)
2. Mobile devices (iOS Safari, Android Chrome)
3. Tablet devices
4. Different screen sizes

## Support

For questions or customization help:
- Review the code comments
- Check browser developer tools for any errors
- Ensure all files are properly linked

## License

This template is provided as-is for your use. Customize freely for your project.

---

**Built with resilience and purpose** 🚀
