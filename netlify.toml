[build]
  # Static site - no build command needed
  publish = "."

[[headers]]
  # Security headers for all pages
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    # Allow OpenAI API calls and external resources
    Content-Security-Policy = "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; connect-src 'self' https://api.openai.com; img-src 'self' data: https:;"

[[headers]]
  # Cache static assets for better performance
  for = "/*.css"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

[[headers]]
  for = "/*.js"
  [headers.values]
    Cache-Control = "public, max-age=31536000"

# Form handling for signup form
[[forms]]
  # This enables Netlify form handling for your signup form
  # Just add netlify attribute to your form in HTML

# 404 redirect to home page
[[redirects]]
  from = "/404"
  to = "/"
  status = 404

# Pretty URLs
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200
  force = false
